"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import dynamic from "next/dynamic";
import "@uiw/react-textarea-code-editor/dist.css";

const CodeEditor = dynamic(
  () => import("@uiw/react-textarea-code-editor").then((mod) => mod.default),
  { ssr: false }
);


interface HtmlEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  rows?: number
}

export function HtmlEditor({ value, onChange, placeholder, className, rows = 12 }: HtmlEditorProps) {
  return (
    <div>
      <CodeEditor
        value={value}
        language="html"
        placeholder={placeholder}
        onChange={(evn) => onChange(evn.target.value)}
        padding={15}
        style={{
          fontSize: 14,
          backgroundColor: "#f8f9fa",
          fontFamily: 'ui-monospace,SFMono-Regular,SF Mono,Consolas,Liberation Mono,Menlo,monospace', minHeight: `${rows * 1.5}rem`,
          borderRadius: '6px',
          border: '1px solid hsl(var(--border))',
        }}
        data-color-mode="light"

        className={cn(
          "w-full resize-none outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          "placeholder:text-muted-foreground"
        )}
      />
    </div>
  )
}
