"use client"

import React, { useState, useEffect } from "react"
import { Save, Eye } from "lucide-react"

interface Variable {
  name: string
  defaultValue: string
}

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { HtmlEditor } from "@/components/ui/html-editor"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import { HeaderFooter } from "@/types/block"
import { useUpdateHeaderFooter } from "@/hooks/use-update-header-footer"
import { useBrands } from "@/hooks/use-brands"
import { useLanguages } from "@/hooks/use-languages"

interface HeaderFooterFormData {
  name: string
  brand: string
  element_type: 'header' | 'footer'
  language: string
  html_content: string
  variables: Record<string, string>
  is_active: boolean
}

interface EditHeaderFooterModalProps {
  headerFooter: HeaderFooter | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function EditHeaderFooterModal({
  headerFooter,
  open,
  onOpenChange,
  onSuccess,
}: EditHeaderFooterModalProps) {
  const { updateHeaderFooter, loading: updateLoading } = useUpdateHeaderFooter()
  const { brands, loading: brandsLoading } = useBrands()
  const { languages, loading: languagesLoading } = useLanguages()

  const [formData, setFormData] = useState<HeaderFooterFormData>({
    name: "",
    brand: "",
    element_type: "header",
    language: "",
    html_content: "",
    variables: {},
    is_active: true,
  })

  const [variables, setVariables] = useState<Variable[]>([])
  const [previewHtml, setPreviewHtml] = useState("")

  // Reset form when headerFooter changes
  useEffect(() => {
    if (headerFooter) {
      setFormData({
        name: headerFooter.name,
        brand: headerFooter.brand,
        element_type: headerFooter.element_type as "header" | "footer",
        language: headerFooter.language,
        html_content: headerFooter.html_content,
        variables: headerFooter.variables || {},
        is_active: headerFooter.is_active,
      })
    }
  }, [headerFooter])

  // Update preview HTML when content or variables change
  useEffect(() => {
    let html = formData.html_content

    // Replace variables in HTML with their default values
    Object.entries(formData.variables).forEach(([varName, defaultValue]) => {
      const regex = new RegExp(`\\{\\{\\s*${varName}\\s*\\}\\}`, 'g')
      html = html.replace(regex, defaultValue)
    })

    setPreviewHtml(html)
  }, [formData.html_content, formData.variables])

  // Extract variables from HTML content
  useEffect(() => {
    const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g
    const foundVariables = new Set<string>()
    let match

    while ((match = variableRegex.exec(formData.html_content)) !== null) {
      foundVariables.add(match[1])
    }

    // Update variables list
    const newVariables: Variable[] = Array.from(foundVariables).map(varName => {
      const existing = variables.find(v => v.name === varName)
      return existing || { name: varName, defaultValue: formData.variables[varName] || '' }
    })

    setVariables(newVariables)

    // Update form variables
    const newFormVariables: Record<string, string> = {}
    newVariables.forEach(variable => {
      newFormVariables[variable.name] = variable.defaultValue
    })

    setFormData(prev => ({ ...prev, variables: newFormVariables }))
  }, [formData.html_content])

  const handleVariableChange = (varName: string, defaultValue: string) => {
    setVariables(prev =>
      prev.map(v => v.name === varName ? { ...v, defaultValue } : v)
    )

    setFormData(prev => ({
      ...prev,
      variables: { ...prev.variables, [varName]: defaultValue }
    }))
  }

  const handleSubmit = async () => {
    if (!headerFooter) return

    if (!formData.name.trim()) {
      toast.error('Header/Footer name is required')
      return
    }

    if (!formData.brand) {
      toast.error('Please select a brand')
      return
    }

    if (!formData.language) {
      toast.error('Please select a language')
      return
    }

    if (!formData.html_content.trim()) {
      toast.error('HTML content is required')
      return
    }

    try {
      await updateHeaderFooter({
        id: headerFooter.id,
        ...formData,
      })
      toast.success('Header/Footer updated successfully!')
      onOpenChange(false)
      onSuccess?.()
    } catch (error) {
      console.error('Failed to update header/footer:', error)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle>Edit Header/Footer</DialogTitle>
              <DialogDescription>
                Update the header/footer details below.
              </DialogDescription>
            </div>
            <div className="flex space-x-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button onClick={handleSubmit} disabled={updateLoading}>
                <Save className="mr-2 h-4 w-4" />
                {updateLoading ? 'Updating...' : 'Update Header/Footer'}
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header/Footer Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                placeholder="Enter header/footer name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-element_type">Type</Label>
              <Select
                value={formData.element_type}
                onValueChange={(value: 'header' | 'footer') => setFormData(prev => ({ ...prev, element_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="header">Header</SelectItem>
                  <SelectItem value="footer">Footer</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-brand">Brand</Label>
              <Select
                value={formData.brand}
                onValueChange={(value) => setFormData(prev => ({ ...prev, brand: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={brandsLoading ? "Loading brands..." : "Select brand"} />
                </SelectTrigger>
                <SelectContent>
                  {brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-language">Language</Label>
              <Select
                value={formData.language}
                onValueChange={(value) => setFormData(prev => ({ ...prev, language: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={languagesLoading ? "Loading languages..." : "Select language"} />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((language) => (
                    <SelectItem key={language.language} value={language.language}>
                      {language.language_display}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <Label className="text-base">Active</Label>
              <p className="text-sm text-muted-foreground">
                Enable this header/footer to make it available for use
              </p>
            </div>
            <Switch
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
            />
          </div>

          {/* HTML Editor and Preview - Side by Side */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Panel - HTML Editor and Variables */}
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium">HTML Content</Label>
                <p className="text-sm text-muted-foreground">
                  Write your HTML content for the header/footer. Use {`{{ variableName }}`} for variables.
                </p>
              </div>
              <HtmlEditor
                placeholder="Enter your HTML content here..."
                value={formData.html_content}
                onChange={(value) => setFormData(prev => ({ ...prev, html_content: value }))}
                rows={16}
              />

              {/* Variables */}
              {variables.length > 0 && (
                <div className="space-y-4">
                  <div>
                    <Label className="text-base font-medium">Variables</Label>
                    <p className="text-sm text-muted-foreground">
                      Set default values for variables found in your HTML
                    </p>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {variables.map((variable) => (
                      <div key={variable.name} className="space-y-2">
                        <Label htmlFor={`edit-var-${variable.name}`}>
                          {`{{ ${variable.name} }}`}
                        </Label>
                        <Input
                          id={`edit-var-${variable.name}`}
                          placeholder={`Default value for ${variable.name}`}
                          value={variable.defaultValue}
                          onChange={(e) => handleVariableChange(variable.name, e.target.value)}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Right Panel - Preview */}
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Live Preview
                </Label>
                <p className="text-sm text-muted-foreground">
                  See how your header/footer will look with variable values
                </p>
              </div>
              <div className="border rounded-lg p-6 min-h-[400px] bg-white shadow-inner">
                {previewHtml ? (
                  <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Start typing HTML to see preview</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
