import { create } from 'zustand'
import { UserSessionInfo } from '@/types/user'
import { signOut } from 'next-auth/react'

interface UserStore {
  user: UserSessionInfo | null
  isRefreshing: boolean
  refreshTimer: NodeJS.Timeout | null
  setUser: (user: UserSessionInfo | null) => void
  setIsRefreshing: (isRefreshing: boolean) => void
  refreshToken: (refreshToken: string) => Promise<boolean>
  scheduleTokenRefresh: (expiresAt: string) => void
  clearRefreshTimer: () => void
  logout: () => void
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://172.16.249.87/apiv1'

const useUserStore = create<UserStore>((set, get) => ({
  user: null,
  isRefreshing: false,
  refreshTimer: null,

  setUser: (user: UserSessionInfo | null) => {
    set({ user })

    // Schedule token refresh if user has valid session
    if (user?.session?.access_token_expires_at) {
      get().scheduleTokenRefresh(user.session.access_token_expires_at)
    }
  },

  setIsRefreshing: (isRefreshing: boolean) => set({ isRefreshing }),

  refreshToken: async (refreshToken: string): Promise<boolean> => {
    const { setIsRefreshing, setUser, logout } = get()

    try {
      setIsRefreshing(true)
      console.log('🔄 Refreshing Django access token...')

      const response = await fetch(`${API_BASE_URL}/users/token/refresh/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh: refreshToken
        }),
      })

      if (!response.ok) {
        console.error('❌ Token refresh failed:', response.statusText)

        // If refresh token is invalid, logout user
        if (response.status === 401) {
          console.log('🚪 Refresh token expired, logging out...')
          logout()
          return false
        }

        throw new Error(`Token refresh failed: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('✅ Token refreshed successfully')

      // Update user session with new tokens
      const currentUser = get().user
      if (currentUser) {
        const updatedUser: UserSessionInfo = {
          ...currentUser,
          session: {
            ...currentUser.session,
            accessToken: data.access,
            refreshToken: data.refresh || refreshToken, // Use new refresh token if provided
            token_created_at: new Date().toISOString(),
            access_token_expires_at: data.access_token_expires_at ||
              new Date(Date.now() + 60 * 60 * 1000).toISOString(), // Default 1 hour
            refresh_token_expires_at: data.refresh_token_expires_at ||
              currentUser.session.refresh_token_expires_at,
          }
        }
        setUser(updatedUser)
      }

      return true
    } catch (error) {
      console.error('❌ Error refreshing token:', error)
      return false
    } finally {
      setIsRefreshing(false)
    }
  },

  scheduleTokenRefresh: (expiresAt: string) => {
    const { clearRefreshTimer, refreshToken } = get()

    // Clear existing timer
    clearRefreshTimer()

    const expirationTime = new Date(expiresAt).getTime()
    const currentTime = Date.now()
    const timeUntilExpiry = expirationTime - currentTime

    // If token is already expired, try to refresh immediately
    if (timeUntilExpiry <= 0) {
      console.log('⚠️ Token already expired, attempting refresh...')
      const user = get().user
      if (user?.session?.refreshToken) {
        refreshToken(user.session.refreshToken)
      }
      return
    }

    // Schedule refresh 5 minutes before expiry (or halfway if less than 10 minutes)
    const refreshBuffer = Math.min(5 * 60 * 1000, timeUntilExpiry / 2) // 5 minutes or half the time
    const refreshTime = timeUntilExpiry - refreshBuffer

    console.log(`⏰ Scheduling token refresh in ${Math.round(refreshTime / 1000 / 60)} minutes`)

    const timer = setTimeout(async () => {
      const user = get().user
      if (user?.session?.refreshToken) {
        const success = await refreshToken(user.session.refreshToken)
        if (!success) {
          console.log('🚪 Token refresh failed, user will need to re-authenticate')
        }
      }
    }, refreshTime)

    set({ refreshTimer: timer })
  },

  clearRefreshTimer: () => {
    const { refreshTimer } = get()
    if (refreshTimer) {
      clearTimeout(refreshTimer)
      set({ refreshTimer: null })
    }
  },

  logout: () => {
    const { clearRefreshTimer } = get()
    clearRefreshTimer()
    set({ user: null, isRefreshing: false })

    // Sign out from NextAuth
    signOut({ callbackUrl: '/login' })
  }
}))

export default useUserStore