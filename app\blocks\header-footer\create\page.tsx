"use client"

import React, { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useSession } from "next-auth/react"
import { ArrowLeft, Save, Eye, Plus } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { HtmlEditor } from "@/components/ui/html-editor"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import { useCreateHeaderFooter } from "@/hooks/use-create-header-footer"
import { useBrands } from "@/hooks/use-brands"
import { useLanguages } from "@/hooks/use-languages"

interface Variable {
  name: string
  defaultValue: string
}

interface HeaderFooterFormData {
  name: string
  brand: string
  element_type: 'header' | 'footer'
  language: string
  html_content: string
  variables: Record<string, string>
  is_active: boolean
}

export default function CreateHeaderFooterPage() {
  const router = useRouter()
  const { data: session, status } = useSession()
  const { createHeaderFooter, loading: createLoading, error: createError } = useCreateHeaderFooter()
  const { brands, loading: brandsLoading } = useBrands()
  const { languages, loading: languagesLoading } = useLanguages()

  const [formData, setFormData] = useState<HeaderFooterFormData>({
    name: "",
    brand: "",
    element_type: "header",
    language: "",
    html_content: "",
    variables: {},
    is_active: true,
  })

  const [variables, setVariables] = useState<Variable[]>([])
  const [previewHtml, setPreviewHtml] = useState("")

  // Update preview HTML when content or variables change
  useEffect(() => {
    let html = formData.html_content

    // Replace variables in HTML with their default values
    Object.entries(formData.variables).forEach(([varName, defaultValue]) => {
      const regex = new RegExp(`\\{\\{\\s*${varName}\\s*\\}\\}`, 'g')
      html = html.replace(regex, defaultValue)
    })

    setPreviewHtml(html)
  }, [formData.html_content, formData.variables])

  // Extract variables from HTML content
  useEffect(() => {
    const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g
    const foundVariables = new Set<string>()
    let match

    while ((match = variableRegex.exec(formData.html_content)) !== null) {
      foundVariables.add(match[1])
    }

    // Update variables list
    const newVariables: Variable[] = Array.from(foundVariables).map(varName => {
      const existing = variables.find(v => v.name === varName)
      return existing || { name: varName, defaultValue: '' }
    })

    setVariables(newVariables)

    // Update form variables
    const newFormVariables: Record<string, string> = {}
    newVariables.forEach(variable => {
      newFormVariables[variable.name] = variable.defaultValue
    })

    setFormData(prev => ({ ...prev, variables: newFormVariables }))
  }, [formData.html_content])

  const handleVariableChange = (varName: string, defaultValue: string) => {
    setVariables(prev =>
      prev.map(v => v.name === varName ? { ...v, defaultValue } : v)
    )

    setFormData(prev => ({
      ...prev,
      variables: { ...prev.variables, [varName]: defaultValue }
    }))
  }

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      toast.error('Header/Footer name is required')
      return
    }

    if (!formData.brand) {
      toast.error('Please select a brand')
      return
    }

    if (!formData.language) {
      toast.error('Please select a language')
      return
    }

    if (!formData.html_content.trim()) {
      toast.error('HTML content is required')
      return
    }

    try {
      await createHeaderFooter(formData)
      toast.success('Header/Footer created successfully!')
      router.push('/blocks/header-footer')
    } catch (error) {
      console.error('Error creating header/footer:', error)
    }
  }

  const loadHeaderExample = () => {
    const exampleHtml = `
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px 0; text-align: center; color: white; font-family: Arial, sans-serif;">
        <div style="max-width: 600px; margin: 0 auto; padding: 0 20px;">
            <h1 style="margin: 0; font-size: 28px; font-weight: bold; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                Welcome to {{ company_name }}
            </h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">
                {{ header_subtitle }}
            </p>
        </div>
    </div>`

    setFormData(prev => ({
      ...prev,
      name: "Example Header",
      html_content: exampleHtml
    }))
  }

  const loadFooterExample = () => {
    const exampleHtml = `
    <div style="background-color: #2c3e50; color: #ecf0f1; padding: 30px 20px; font-family: Arial, sans-serif; text-align: center;">
        <div style="max-width: 600px; margin: 0 auto;">
            <div style="border-bottom: 1px solid #34495e; padding-bottom: 20px; margin-bottom: 20px;">
                <h3 style="margin: 0 0 10px 0; font-size: 20px; color: #3498db;">
                    Stay Connected
                </h3>
                <div style="margin: 15px 0;">
                    <a href="{{ facebook_url }}" style="color: #3498db; text-decoration: none; margin: 0 10px; font-size: 14px;">Facebook</a>
                    <a href="{{ twitter_url }}" style="color: #3498db; text-decoration: none; margin: 0 10px; font-size: 14px;">Twitter</a>
                    <a href="{{ instagram_url }}" style="color: #3498db; text-decoration: none; margin: 0 10px; font-size: 14px;">Instagram</a>
                    <a href="{{ linkedin_url }}" style="color: #3498db; text-decoration: none; margin: 0 10px; font-size: 14px;">LinkedIn</a>
                </div>
            </div>
            <div style="font-size: 12px; color: #95a5a6; line-height: 1.5;">
                <p style="margin: 0 0 10px 0;">
                    © {{ current_year }} {{ company_name }}. All rights reserved.
                </p>
                <p style="margin: 0;">
                    You received this email because you subscribed to our newsletter.
                    <a href="{{ unsubscribe_url }}" style="color: #3498db; text-decoration: none;">Unsubscribe</a>
                </p>
            </div>
        </div>
    </div>`

    setFormData(prev => ({
      ...prev,
      name: "Example Footer",
      html_content: exampleHtml
    }))
  }

  // Show loading while session is being fetched
  if (status === "loading") {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="text-muted-foreground">Loading...</div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (status === "unauthenticated") {
    router.push('/login')
    return null
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create Header/Footer</h1>
          <p className="text-muted-foreground">
            Create a new header or footer for your newsletters
          </p>
        </div>
        <Button onClick={handleSubmit} disabled={createLoading}>
          <Save className="mr-2 h-4 w-4" />
          {createLoading ? 'Creating...' : 'Create Header/Footer'}
        </Button>
      </div>



      {/* Error Display */}
      {createError && (
        <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          Error: {createError}
        </div>
      )}

      {/* Header/Footer Details */}
      <Card>
        <CardHeader>
          <CardTitle>Header/Footer Details</CardTitle>
          <CardDescription>
            Configure your header/footer settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                placeholder="Enter header/footer name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="element_type">Type</Label>
              <Select
                value={formData.element_type}
                onValueChange={(value: 'header' | 'footer') => setFormData(prev => ({ ...prev, element_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="header">Header</SelectItem>
                  <SelectItem value="footer">Footer</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand">Brand</Label>
              <Select
                value={formData.brand}
                onValueChange={(value) => setFormData(prev => ({ ...prev, brand: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={brandsLoading ? "Loading brands..." : "Select brand"} />
                </SelectTrigger>
                <SelectContent>
                  {brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="language">Language</Label>
              <Select
                value={formData.language}
                onValueChange={(value) => setFormData(prev => ({ ...prev, language: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder={languagesLoading ? "Loading languages..." : "Select language"} />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((language) => (
                    <SelectItem key={language.language} value={language.language}>
                      {language.language_display}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="mt-6">
            <div className="flex items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label className="text-base">Active</Label>
                <p className="text-sm text-muted-foreground">
                  Enable this header/footer to make it available for use
                </p>
              </div>
              <Switch
                checked={formData.is_active}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* HTML Editor and Preview - Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Panel - HTML Editor and Variables */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>HTML Content</CardTitle>
                  <CardDescription>
                    Write your HTML content for the header/footer. Use {`{{ variableName }}`} for variables.
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadHeaderExample}
                    disabled={formData.element_type !== 'header'}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Header Example
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadFooterExample}
                    disabled={formData.element_type !== 'footer'}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Footer Example
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <HtmlEditor
                placeholder="Enter your HTML content here..."
                value={formData.html_content}
                onChange={(value) => setFormData(prev => ({ ...prev, html_content: value }))}
                rows={20}
              />
            </CardContent>
          </Card>

          {/* Variables */}
          {variables.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Variables</CardTitle>
                <CardDescription>
                  Set default values for variables found in your HTML
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {variables.map((variable) => (
                    <div key={variable.name} className="space-y-2">
                      <Label htmlFor={`var-${variable.name}`}>
                        {`{{ ${variable.name} }}`}
                      </Label>
                      <Input
                        id={`var-${variable.name}`}
                        placeholder={`Default value for ${variable.name}`}
                        value={variable.defaultValue}
                        onChange={(e) => handleVariableChange(variable.name, e.target.value)}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Right Panel - Preview */}
        <Card className="sticky top-6 max-h-[calc(100vh-48px)] overflow-y-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Live Preview
            </CardTitle>
            <CardDescription>
              See how your header/footer will look with variable values
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-6 min-h-[500px] bg-white shadow-inner">
              {previewHtml ? (
                <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  <div className="text-center">
                    <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Start typing HTML to see preview</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
