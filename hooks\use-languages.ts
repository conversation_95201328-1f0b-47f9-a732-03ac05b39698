import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'

export interface Language {
  language: string
  language_display: string
}

interface UseLanguagesReturn {
  languages: Language[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useLanguages(): UseLanguagesReturn {
  const { data: session } = useSession()
  const [languages, setLanguages] = useState<Language[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchLanguages = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if user is authenticated
      if (!session) {
        setError('User not authenticated')
        setLanguages([])
        return
      }

      if (!session.djangoAccessToken) {
        setError('Django access token not available. Please try logging in again.')
        setLanguages([])
        return
      }

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://172.16.249.87/apiv1'

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/brands/list-languages/`, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch languages: ${response.statusText}`)
      }

      const data: Language[] = await response.json()
      setLanguages(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setLanguages([])
    } finally {
      setLoading(false)
    }
  }, [session])

  useEffect(() => {
    fetchLanguages()
  }, [fetchLanguages])

  return {
    languages,
    loading,
    error,
    refetch: fetchLanguages
  }
}
