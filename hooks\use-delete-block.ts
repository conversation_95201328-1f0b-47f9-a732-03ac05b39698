import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'

interface UseDeleteBlockReturn {
  deleteBlock: (blockId: string) => Promise<void>
  loading: boolean
  error: string | null
}

export function useDeleteBlock(): UseDeleteBlockReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteBlock = async (blockId: string) => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required. Please log in again.')
    }

    setLoading(true)
    setError(null)

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://10.10.10.10/apiv1'
      
      const response = await fetch(`${backendUrl}/blocks/delete-block/${blockId}/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.djangoAccessToken}`
        }
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `Failed to delete block: ${response.statusText}`)
      }

      toast.success('Block deleted successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete block'
      setError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return {
    deleteBlock,
    loading,
    error
  }
}
