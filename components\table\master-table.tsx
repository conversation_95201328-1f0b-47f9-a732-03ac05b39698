"use client"

import React, { useEffect, useState, ReactN<PERSON>, useMemo } from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  Column,
} from "@tanstack/react-table"
import { 
  ArrowUpDown, 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  Settings2,
  Filter,
  X,
  Search
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Spinner } from "@/components/ui/shadcn-io/spinner"

// Filter configuration types
export interface FilterConfig {
  id: string
  name: string
  type?: 'text' | 'select' | 'date' | 'number'
  options?: { value: string; label: string }[]
}

// Column configuration with enhanced features
export interface MasterColumnDef<T = any> extends Omit<ColumnDef<T>, 'header'> {
  key: string
  header?: string | ((props: any) => ReactNode)
  cell?: (props: any) => ReactNode
  sortable?: boolean
  filterable?: boolean
  filterConfig?: FilterConfig
  width?: string | number
  minWidth?: string | number
  maxWidth?: string | number
}

// Pagination configuration
export interface PaginationConfig {
  page: number
  pageSize: number
  totalCount: number
  hasNext: boolean
  hasPrevious: boolean
  onPageChange: (page: number) => void
  onPageSizeChange: (pageSize: number) => void
  pageSizeOptions?: number[]
}

// Main component props
export interface MasterTableProps<T = any> {
  data: T[]
  columns: MasterColumnDef<T>[]
  loading?: boolean
  error?: string | null
  
  // Pagination
  pagination?: PaginationConfig
  
  // Column management
  enableColumnVisibility?: boolean
  defaultColumnVisibility?: Record<string, boolean>
  onColumnVisibilityChange?: (visibility: VisibilityState) => void
  
  // Filtering
  enableFiltering?: boolean
  globalFilter?: string
  onGlobalFilterChange?: (filter: string) => void
  columnFilters?: ColumnFiltersState
  onColumnFiltersChange?: (filters: ColumnFiltersState) => void
  
  // Sorting
  enableSorting?: boolean
  sorting?: SortingState
  onSortingChange?: (sorting: SortingState) => void
  
  // Selection
  enableRowSelection?: boolean
  rowSelection?: Record<string, boolean>
  onRowSelectionChange?: (selection: Record<string, boolean>) => void
  
  // Styling
  className?: string
  tableClassName?: string
  
  // Custom content
  loadingContent?: ReactNode
  errorContent?: ReactNode
  emptyContent?: ReactNode
  
  // API pagination support
  manualPagination?: boolean
  manualSorting?: boolean
  manualFiltering?: boolean
}

export function MasterTable<T = any>({
  data,
  columns: columnsProp,
  loading = false,
  error = null,
  pagination,
  enableColumnVisibility = true,
  defaultColumnVisibility = {},
  onColumnVisibilityChange,
  enableFiltering = true,
  globalFilter = "",
  onGlobalFilterChange,
  columnFilters: externalColumnFilters,
  onColumnFiltersChange: externalOnColumnFiltersChange,
  enableSorting = true,
  sorting: externalSorting,
  onSortingChange: externalOnSortingChange,
  enableRowSelection = false,
  rowSelection: externalRowSelection,
  onRowSelectionChange: externalOnRowSelectionChange,
  className = "",
  tableClassName = "",
  loadingContent,
  errorContent,
  emptyContent,
  manualPagination = false,
  manualSorting = false,
  manualFiltering = false,
}: MasterTableProps<T>) {
  // Internal state management
  const [internalSorting, setInternalSorting] = useState<SortingState>([])
  const [internalColumnFilters, setInternalColumnFilters] = useState<ColumnFiltersState>([])
  const [internalColumnVisibility, setInternalColumnVisibility] = useState<VisibilityState>(defaultColumnVisibility)
  const [internalRowSelection, setInternalRowSelection] = useState<Record<string, boolean>>({})
  const [internalGlobalFilter, setInternalGlobalFilter] = useState("")

  // Use external state if provided, otherwise use internal state
  const sorting = externalSorting ?? internalSorting
  const setSorting = externalOnSortingChange ? (updater: any) => {
    const newValue = typeof updater === 'function' ? updater(sorting) : updater
    externalOnSortingChange(newValue)
  } : setInternalSorting

  const columnFilters = externalColumnFilters ?? internalColumnFilters
  const setColumnFilters = externalOnColumnFiltersChange ? (updater: any) => {
    const newValue = typeof updater === 'function' ? updater(columnFilters) : updater
    externalOnColumnFiltersChange(newValue)
  } : setInternalColumnFilters

  const columnVisibility = internalColumnVisibility
  const setColumnVisibility = onColumnVisibilityChange ? (updater: any) => {
    const newValue = typeof updater === 'function' ? updater(columnVisibility) : updater
    onColumnVisibilityChange(newValue)
    setInternalColumnVisibility(newValue)
  } : setInternalColumnVisibility

  const rowSelection = externalRowSelection ?? internalRowSelection
  const setRowSelection = externalOnRowSelectionChange ? (updater: any) => {
    const newValue = typeof updater === 'function' ? updater(rowSelection) : updater
    externalOnRowSelectionChange(newValue)
  } : setInternalRowSelection

  const currentGlobalFilter = globalFilter || internalGlobalFilter
  const setGlobalFilter = onGlobalFilterChange ? (updater: any) => {
    const newValue = typeof updater === 'function' ? updater(currentGlobalFilter) : updater
    onGlobalFilterChange(newValue)
  } : setInternalGlobalFilter

  // Convert MasterColumnDef to TanStack ColumnDef
  const columns = useMemo<ColumnDef<T>[]>(() => {
    return columnsProp.map((col) => {
      const tanStackCol: ColumnDef<T> = {
        ...col,
        accessorKey: col.key,
        id: col.key,
        enableSorting: enableSorting && (col.sortable !== false),
        enableColumnFilter: enableFiltering && (col.filterable !== false),
      }

      // Handle header
      if (typeof col.header === 'string') {
        if (col.sortable !== false && enableSorting) {
          tanStackCol.header = ({ column }) => (
            <Button
              variant="ghost"
              onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
              className="h-auto p-0 font-medium hover:bg-transparent"
            >
              {col.header as string}
              <ArrowUpDown className="ml-2 h-4 w-4" />
            </Button>
          )
        } else {
          tanStackCol.header = col.header as string
        }
      } else if (typeof col.header === 'function') {
        tanStackCol.header = col.header
      } else {
        tanStackCol.header = col.key
      }

      // Handle cell
      if (col.cell) {
        tanStackCol.cell = col.cell
      }

      return tanStackCol
    })
  }, [columnsProp, enableSorting, enableFiltering])

  // Initialize table
  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter: currentGlobalFilter,
      ...(pagination && manualPagination ? {
        pagination: {
          pageIndex: pagination.page - 1,
          pageSize: pagination.pageSize,
        },
      } : {}),
    },
    enableRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    ...(pagination && manualPagination ? {} : { getPaginationRowModel: getPaginationRowModel() }),
    ...(pagination && manualPagination ? {
      manualPagination: true,
      pageCount: Math.ceil(pagination.totalCount / pagination.pageSize),
    } : {}),
    manualSorting,
    manualFiltering,
  })

  // Column filter component
  const ColumnFilter = ({ column }: { column: Column<T, unknown> }) => {
    const columnMeta = columnsProp.find(col => col.key === column.id)
    const filterConfig = columnMeta?.filterConfig

    if (!filterConfig) {
      return (
        <Input
          placeholder={`Filter ${column.id}...`}
          value={(column.getFilterValue() as string) ?? ""}
          onChange={(event) => column.setFilterValue(event.target.value)}
          className="h-8 w-[150px]"
        />
      )
    }

    switch (filterConfig.type) {
      case 'select':
        return (
          <Select
            value={(column.getFilterValue() as string) ?? ""}
            onValueChange={(value) => column.setFilterValue(value === "all" ? "" : value)}
          >
            <SelectTrigger className="h-8 w-[150px]">
              <SelectValue placeholder={`Filter ${filterConfig.name}...`} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {filterConfig.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      case 'number':
        return (
          <Input
            type="number"
            placeholder={`Filter ${filterConfig.name}...`}
            value={(column.getFilterValue() as string) ?? ""}
            onChange={(event) => column.setFilterValue(event.target.value)}
            className="h-8 w-[150px]"
          />
        )
      case 'date':
        return (
          <Input
            type="date"
            value={(column.getFilterValue() as string) ?? ""}
            onChange={(event) => column.setFilterValue(event.target.value)}
            className="h-8 w-[150px]"
          />
        )
      default:
        return (
          <Input
            placeholder={`Filter ${filterConfig.name}...`}
            value={(column.getFilterValue() as string) ?? ""}
            onChange={(event) => column.setFilterValue(event.target.value)}
            className="h-8 w-[150px]"
          />
        )
    }
  }

  // Loading state
  if (loading) {
    return (
      <div className={`w-full ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-2">
            <Spinner variant="infinite" size={64} />
            <div className="text-center animate-pulse">
              {loadingContent || "Loading..."}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className={`w-full ${className}`}>
        <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          {errorContent || `Error: ${error}`}
        </div>
      </div>
    )
  }

  return (
    <div className={`w-full space-y-4 ${className}`}>
      {/* Table Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* Global Search */}
          {enableFiltering && (
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search all columns..."
                value={currentGlobalFilter}
                onChange={(event) => setGlobalFilter(event.target.value)}
                className="h-8 w-[150px] lg:w-[250px]"
              />
              {currentGlobalFilter && (
                <Button
                  variant="ghost"
                  onClick={() => setGlobalFilter("")}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Column Visibility */}
          {enableColumnVisibility && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="ml-auto">
                  <Settings2 className="mr-2 h-4 w-4" />
                  View
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[150px]">
                <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    const columnMeta = columnsProp.find(col => col.key === column.id)
                    const displayName = columnMeta?.header || column.id
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                      >
                        {typeof displayName === 'string' ? displayName : column.id}
                      </DropdownMenuCheckboxItem>
                    )
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      {/* Active Filters */}
      {enableFiltering && columnFilters.length > 0 && (
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {columnFilters.map((filter) => {
            const columnMeta = columnsProp.find(col => col.key === filter.id)
            const displayName = columnMeta?.header || filter.id
            return (
              <div
                key={filter.id}
                className="flex items-center space-x-1 bg-muted px-2 py-1 rounded text-sm"
              >
                <span>{typeof displayName === 'string' ? displayName : filter.id}: {String(filter.value)}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0"
                  onClick={() => {
                    const column = table.getColumn(filter.id)
                    column?.setFilterValue("")
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            )
          })}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => table.resetColumnFilters()}
            className="h-6 px-2 text-xs"
          >
            Clear all
          </Button>
        </div>
      )}

      {/* Table */}
      <div className="rounded-md border">
        <Table className={tableClassName}>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  const columnMeta = columnsProp.find(col => col.key === header.column.id)
                  return (
                    <TableHead
                      key={header.id}
                      style={{
                        width: columnMeta?.width,
                        minWidth: columnMeta?.minWidth,
                        maxWidth: columnMeta?.maxWidth,
                      }}
                    >
                      <div className="flex items-center space-x-2">
                        <div>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </div>
                        {/* Column Filter */}
                        {enableFiltering && columnMeta?.filterable !== false && (
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                              >
                                <Filter className="h-3 w-3" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-80" align="start">
                              <div className="space-y-2">
                                <Label htmlFor={`filter-${header.column.id}`}>
                                  Filter {typeof columnMeta?.header === 'string' ? columnMeta.header : header.column.id}
                                </Label>
                                <ColumnFilter column={header.column} />
                              </div>
                            </PopoverContent>
                          </Popover>
                        )}
                      </div>
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {emptyContent || "No results."}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            {enableRowSelection && (
              <span>
                {table.getFilteredSelectedRowModel().rows.length} of{" "}
                {table.getFilteredRowModel().rows.length} row(s) selected.
              </span>
            )}
            {!enableRowSelection && (
              <span>
                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{" "}
                {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of{" "}
                {pagination.totalCount} entries
              </span>
            )}
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Rows per page</p>
              <Select
                value={`${pagination.pageSize}`}
                onValueChange={(value) => {
                  pagination.onPageSizeChange(Number(value))
                  pagination.onPageChange(1) // Reset to first page when changing page size
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue placeholder={pagination.pageSize} />
                </SelectTrigger>
                <SelectContent side="top">
                  {(pagination.pageSizeOptions || [10, 20, 30, 40, 50]).map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Page {pagination.page} of{" "}
              {Math.ceil(pagination.totalCount / pagination.pageSize)}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Go to first page</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page - 1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Go to previous page</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Go to next page</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(Math.ceil(pagination.totalCount / pagination.pageSize))}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Go to last page</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
