import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { HeaderFooter } from '@/types/block'

export interface UpdateHeaderFooterRequest {
  id: string
  name: string
  brand: string
  element_type: 'header' | 'footer'
  language: string
  html_content: string
  variables: Record<string, string>
  is_active: boolean
}

interface UseUpdateHeaderFooterReturn {
  updateHeaderFooter: (data: UpdateHeaderFooterRequest) => Promise<HeaderFooter>
  loading: boolean
  error: string | null
}

export function useUpdateHeaderFooter(): UseUpdateHeaderFooterReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateHeaderFooter = useCallback(async (data: UpdateHeaderFooterRequest): Promise<HeaderFooter> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    try {
      setLoading(true)
      setError(null)

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://172.16.249.87/apiv1'

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/blocks/update-header-footer/`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `Failed to update header/footer: ${response.statusText}`)
      }

      const headerFooter: HeaderFooter = await response.json()
      return headerFooter
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken])

  return {
    updateHeaderFooter,
    loading,
    error
  }
}
