"use client"

import React, { useEffect, useMem<PERSON>, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useSession } from "next-auth/react"
import { NextPage } from 'next'
import { Plus } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useBrands } from "@/hooks/use-brands"
import { useBlocks } from "@/hooks/use-blocks"
import { useCreateTemplate } from "@/hooks/use-create-template"
import { toast } from "sonner"

interface Props {}

const Page: NextPage<Props> = ({}) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { status } = useSession()
  const { brands } = useBrands()
  const [brandFilter, setBrandFilter] = useState<string>("")
  const { blocks } = useBlocks({ page: 1, pageSize: 100, brand: brandFilter || undefined })
  const { createTemplate, loading } = useCreateTemplate()

  const [formData, setFormData] = useState({
    name: "",
    brand: "",
    description: "",
    is_active: true,
    template_blocks: [] as { block_id: string; order_position: number }[],
  })

  // Initialize brand from URL params (?brand= or ?marca=)
  useEffect(() => {
    const spBrand = searchParams?.get('brand') || searchParams?.get('marca')
    if (spBrand) {
      setFormData(prev => ({ ...prev, brand: spBrand }))
      setBrandFilter(spBrand)
    }
  }, [searchParams])

  // Keep hook filter in sync with selected brand
  useEffect(() => {
    setBrandFilter(formData.brand)
  }, [formData.brand])

  const availableBlocks = useMemo(() => blocks, [blocks])

  // Build live preview HTML
  const previewHtml = useMemo(() => {
    let html = ""
    for (const tb of formData.template_blocks) {
      const block = availableBlocks.find(b => b.id === tb.block_id)
      if (!block) continue
      let content = block.html_content || ""
      if (block.variables && typeof block.variables === 'object') {
        Object.entries(block.variables).forEach(([varName, defaultValue]) => {
          const regex = new RegExp(`\\{\\{\\s*${varName}\\s*\\}}`, 'g')
          content = content.replace(regex, String(defaultValue ?? ''))
        })
      }
      html += `\n<!-- Bloc: ${block.name} (#${tb.order_position}) -->\n` + content + "\n"
    }
    return html
  }, [formData.template_blocks, availableBlocks])

  const handleAddBlock = (blockId: string) => {
    setFormData((prev) => ({
      ...prev,
      template_blocks: [
        ...prev.template_blocks,
        { block_id: blockId, order_position: prev.template_blocks.length + 1 },
      ],
    }))
  }

  const handleRemoveBlock = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      template_blocks: prev.template_blocks
        .filter((_, i) => i !== index)
        .map((tb, i) => ({ ...tb, order_position: i + 1 })),
    }))
  }

  const moveBlock = (index: number, direction: -1 | 1) => {
    setFormData((prev) => {
      const newBlocks = [...prev.template_blocks]
      const targetIndex = index + direction
      if (targetIndex < 0 || targetIndex >= newBlocks.length) return prev
      const [moved] = newBlocks.splice(index, 1)
      newBlocks.splice(targetIndex, 0, moved)
      return {
        ...prev,
        template_blocks: newBlocks.map((tb, i) => ({ ...tb, order_position: i + 1 })),
      }
    })
  }

  const handleSubmit = async () => {
    if (!formData.name.trim()) return toast.error('El nom és obligatori')
    if (!formData.brand) return toast.error('Selecciona una marca')
    if (formData.template_blocks.length === 0) return toast.error('Afegeix almenys un bloc')

    try {
      await createTemplate(formData)
      router.push('/templates')
    } catch (e) {
      console.error(e)
    }
  }

  if (status === "loading") {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="text-muted-foreground">Carregant...</div>
        </div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    router.push('/login')
    return null
  }

  return (
    <div className="p-6 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Crear Plantilla</h1>
          <p className="text-muted-foreground">Defineix una nova plantilla amb blocs</p>
        </div>
        <Button onClick={handleSubmit} disabled={loading}>
          <Plus className="mr-2 h-4 w-4" />
          {loading ? 'Creant...' : 'Crear'}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Informació bàsica</CardTitle>
              <CardDescription>Nom, marca i estat</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Nom</Label>
                <Input value={formData.name} onChange={(e) => setFormData((p) => ({ ...p, name: e.target.value }))} />
              </div>
              <div className="space-y-2">
                <Label>Marca</Label>
                <Select value={formData.brand} onValueChange={(v) => { setFormData((p) => ({ ...p, brand: v, template_blocks: [] })); setBrandFilter(v) }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona una marca" />
                  </SelectTrigger>
                  <SelectContent>
                    {brands.map((b) => (
                      <SelectItem key={b.id} value={b.id}>{b.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>Descripció</Label>
                <Input value={formData.description} onChange={(e) => setFormData((p) => ({ ...p, description: e.target.value }))} />
              </div>
              <div className="flex items-center justify-between">
                <Label>Activa</Label>
                <Switch checked={formData.is_active} onCheckedChange={(v) => setFormData((p) => ({ ...p, is_active: v }))} />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Blocs de la plantilla</CardTitle>
              <CardDescription>Afegir, treure i ordenar blocs</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Blocks table - disabled until brand is selected */}
              {!formData.brand && (
                <div className="p-4 text-sm text-muted-foreground border rounded">
                  Selecciona una marca per veure i afegir blocs.
                </div>
              )}

              {formData.brand && (
                <div className="overflow-hidden rounded-lg border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50%]">Nom del bloc</TableHead>
                        <TableHead>Marca</TableHead>
                        <TableHead className="text-right">Accions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {availableBlocks.length ? (
                        availableBlocks.map((b) => (
                          <TableRow key={b.id}>
                            <TableCell className="font-medium">{b.name}</TableCell>
                            <TableCell>{b.brand_name}</TableCell>
                            <TableCell className="text-right">
                              <Button size="sm" variant="outline" onClick={() => handleAddBlock(b.id)}>
                                Afegir
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center text-sm text-muted-foreground h-16">
                            No hi ha blocs disponibles per a la marca seleccionada.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}

              <ol className="space-y-2 list-decimal pl-6">
                {formData.template_blocks.map((tb, idx) => {
                  const block = availableBlocks.find((b) => b.id === tb.block_id)
                  return (
                    <li key={`${tb.block_id}-${idx}`} className="flex items-center gap-2">
                      <span className="flex-1">{block?.name || tb.block_id}</span>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" onClick={() => moveBlock(idx, -1)}>Amunt</Button>
                        <Button size="sm" variant="outline" onClick={() => moveBlock(idx, 1)}>Avall</Button>
                        <Button size="sm" variant="destructive" onClick={() => handleRemoveBlock(idx)}>Treure</Button>
                      </div>
                    </li>
                  )
                })}
              </ol>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          {/* Live preview */}
          <Card>
            <CardHeader>
              <CardTitle>Previsualització</CardTitle>
              <CardDescription>Mostra del resultat concatenant els blocs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg p-4 bg-white max-h-[70vh] overflow-auto min-w-[320px]">
                {previewHtml ? (
                  <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                ) : (
                  <div className="text-sm text-muted-foreground">Afegeix blocs per veure la previsualització.</div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Accions</CardTitle>
            </CardHeader>
            <CardContent className="flex gap-2">
              <Button onClick={handleSubmit} disabled={loading}>
                {loading ? 'Creant...' : 'Crear Plantilla'}
              </Button>
              <Button variant="outline" onClick={() => router.push('/templates')}>Cancel·lar</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default Page

