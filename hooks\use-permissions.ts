import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { Permission } from '@/types/role'

interface UsePermissionsReturn {
  permissions: Permission[]
  loading: boolean
  error: string | null
  refetch: () => void
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://127.0.0.1:8000'

export function usePermissions(): UsePermissionsReturn {
  const { data: session } = useSession()
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const getAuthHeaders = useCallback(() => {
    return {
      'Content-Type': 'application/json',
      ...(session?.djangoAccessToken && { 'Authorization': `Bearer ${session.djangoAccessToken}` })
    }
  }, [session?.djangoAccessToken])

  const fetchPermissions = useCallback(async () => {
    if (!session?.djangoAccessToken) {
      setError('Authentication required')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`${API_BASE_URL}/users/permissions/`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })
      
      if (!response.ok) {
        throw new Error(`Failed to fetch permissions: ${response.statusText}`)
      }

      const data: Permission[] = await response.json()
      setPermissions(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setPermissions([])
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders])

  useEffect(() => {
    fetchPermissions()
  }, [fetchPermissions])

  return {
    permissions,
    loading,
    error,
    refetch: fetchPermissions
  }
}
