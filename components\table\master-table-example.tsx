"use client"

import React, { useState } from "react"
import { MasterTable, MasterColumnDef, PaginationConfig, FilterConfig } from "./master-table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON>cil, Trash } from "lucide-react"

// Example data type
interface ExampleData {
  id: string
  name: string
  email: string
  status: 'active' | 'inactive' | 'pending'
  role: string
  createdAt: string
  age: number
}

// Example usage component
export function MasterTableExample() {
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [loading, setLoading] = useState(false)

  // Example data
  const data: ExampleData[] = [
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      status: "active",
      role: "Admin",
      createdAt: "2024-01-15",
      age: 30
    },
    {
      id: "2",
      name: "<PERSON>",
      email: "<EMAIL>",
      status: "inactive",
      role: "User",
      createdAt: "2024-01-10",
      age: 25
    },
    {
      id: "3",
      name: "<PERSON>",
      email: "<EMAIL>",
      status: "pending",
      role: "Editor",
      createdAt: "2024-01-20",
      age: 35
    }
  ]

  // Filter configurations
  const statusFilterConfig: FilterConfig = {
    id: "status",
    name: "Status",
    type: "select",
    options: [
      { value: "active", label: "Active" },
      { value: "inactive", label: "Inactive" },
      { value: "pending", label: "Pending" }
    ]
  }

  const roleFilterConfig: FilterConfig = {
    id: "role",
    name: "Role",
    type: "select",
    options: [
      { value: "Admin", label: "Admin" },
      { value: "User", label: "User" },
      { value: "Editor", label: "Editor" }
    ]
  }

  // Column definitions
  const columns: MasterColumnDef<ExampleData>[] = [
    {
      key: "name",
      header: "Name",
      sortable: true,
      filterable: true,
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      key: "email",
      header: "Email",
      sortable: true,
      filterable: true,
      cell: ({ row }) => (
        <div className="text-muted-foreground">{row.getValue("email")}</div>
      ),
    },
    {
      key: "status",
      header: "Status",
      sortable: true,
      filterable: true,
      filterConfig: statusFilterConfig,
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <Badge 
            variant={
              status === "active" ? "default" : 
              status === "inactive" ? "destructive" : 
              "secondary"
            }
          >
            {status}
          </Badge>
        )
      },
    },
    {
      key: "role",
      header: "Role",
      sortable: true,
      filterable: true,
      filterConfig: roleFilterConfig,
      cell: ({ row }) => (
        <div>{row.getValue("role")}</div>
      ),
    },
    {
      key: "age",
      header: "Age",
      sortable: true,
      filterable: true,
      filterConfig: {
        id: "age",
        name: "Age",
        type: "number"
      },
      cell: ({ row }) => (
        <div>{row.getValue("age")}</div>
      ),
    },
    {
      key: "createdAt",
      header: "Created",
      sortable: true,
      filterable: true,
      filterConfig: {
        id: "createdAt",
        name: "Created Date",
        type: "date"
      },
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"))
        return <div>{date.toLocaleDateString()}</div>
      },
    },
    {
      key: "actions",
      header: "Actions",
      sortable: false,
      filterable: false,
      enableHiding: false,
      cell: ({ row }) => {
        const item = row.original
        return (
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => console.log("View", item)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => console.log("Edit", item)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => console.log("Delete", item)}
            >
              <Trash className="h-4 w-4" />
            </Button>
          </div>
        )
      },
    },
  ]

  // Pagination configuration
  const pagination: PaginationConfig = {
    page,
    pageSize,
    totalCount: data.length,
    hasNext: page * pageSize < data.length,
    hasPrevious: page > 1,
    onPageChange: setPage,
    onPageSizeChange: (newPageSize) => {
      setPageSize(newPageSize)
      setPage(1)
    },
    pageSizeOptions: [5, 10, 20, 50]
  }

  return (
    <div className="container mx-auto py-10">
      <div className="space-y-4">
        <div>
          <h1 className="text-2xl font-bold">Master Table Example</h1>
          <p className="text-muted-foreground">
            A comprehensive table component with filtering, sorting, pagination, and column management.
          </p>
        </div>

        <MasterTable
          data={data}
          columns={columns}
          loading={loading}
          pagination={pagination}
          enableColumnVisibility={true}
          enableFiltering={true}
          enableSorting={true}
          enableRowSelection={true}
          manualPagination={false}
          className="w-full"
        />
      </div>
    </div>
  )
}
