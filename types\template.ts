import { Block } from "@/types/block"

export interface TemplateListItem {
  id: string
  name: string
  brand: string
  brand_name: string
  is_active: boolean
  total_blocks: number
  created_at: string
  updated_at: string
}

export interface TemplateResponse {
  count: number
  next: string | null
  previous: string | null
  results: TemplateListItem[]
}

export interface TemplateBlockItem {
  id: string
  block: Block
  order_position: number
  created_at: string
  updated_at: string
}

export interface TemplateDetail {
  id: string
  name: string
  brand: string
  brand_name: string
  description: string
  is_active: boolean
  total_blocks: number
  template_blocks: TemplateBlockItem[]
  created_at: string
  updated_at: string
}

export interface ActiveTemplateItem {
  id: string
  name: string
  description: string
  brand_id: string
  brand_name: string
}

export interface ActiveTemplatesResponse {
  active_templates: ActiveTemplateItem[]
  total_count: number
}

