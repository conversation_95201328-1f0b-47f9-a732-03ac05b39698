import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { Role } from '@/types/role'

interface CreateRoleRequest {
  name: string
  permissions: number[]
}

interface UpdateRoleRequest {
  name: string
  permissions: number[]
}

interface UseRolesReturn {
  roles: Role[]
  loading: boolean
  error: string | null
  refetch: () => void
  createRole: (role: CreateRoleRequest) => Promise<Role>
  updateRole: (id: number, role: UpdateRoleRequest) => Promise<Role>
  deleteRole: (id: number) => Promise<void>
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://127.0.0.1:8000'

export function useRoles(): UseRolesReturn {
  const { data: session } = useSession()
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const getAuthHeaders = useCallback(() => {
    return {
      'Content-Type': 'application/json',
      ...(session?.djangoAccessToken && { 'Authorization': `Bearer ${session.djangoAccessToken}` })
    }
  }, [session?.djangoAccessToken])

  const fetchRoles = useCallback(async () => {
    if (!session?.djangoAccessToken) {
      setError('Authentication required')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`${API_BASE_URL}/users/groups/`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })
      
      if (!response.ok) {
        throw new Error(`Failed to fetch roles: ${response.statusText}`)
      }

      const data: Role[] = await response.json()
      setRoles(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setRoles([])
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders])

  const createRole = useCallback(async (role: CreateRoleRequest): Promise<Role> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    const response = await fetch(`${API_BASE_URL}/users/create-group/`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(role),
    })

    if (!response.ok) {
      throw new Error(`Failed to create role: ${response.statusText}`)
    }

    const newRole: Role = await response.json()
    setRoles(prev => [...prev, newRole])
    return newRole
  }, [session?.djangoAccessToken, getAuthHeaders])

  const updateRole = useCallback(async (id: number, role: UpdateRoleRequest): Promise<Role> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    const response = await fetch(`${API_BASE_URL}/users/update-group/${id}/`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(role),
    })

    if (!response.ok) {
      throw new Error(`Failed to update role: ${response.statusText}`)
    }

    const updatedRole: Role = await response.json()
    setRoles(prev => prev.map(r => r.id === id ? updatedRole : r))
    return updatedRole
  }, [session?.djangoAccessToken, getAuthHeaders])

  const deleteRole = useCallback(async (id: number): Promise<void> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    const response = await fetch(`${API_BASE_URL}/users/groups-delete/${id}/`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    })

    if (!response.ok) {
      throw new Error(`Failed to delete role: ${response.statusText}`)
    }

    setRoles(prev => prev.filter(r => r.id !== id))
  }, [session?.djangoAccessToken, getAuthHeaders])

  useEffect(() => {
    fetchRoles()
  }, [fetchRoles])

  return {
    roles,
    loading,
    error,
    refetch: fetchRoles,
    createRole,
    updateRole,
    deleteRole
  }
}
