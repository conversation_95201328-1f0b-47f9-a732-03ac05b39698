"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { NextPage } from 'next'
import { Plus, Search } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { BlocksTable } from "@/components/table/blocks-table"
import { HeadersFootersTable } from "@/components/table/headers-footers-table"
import { useBlocks } from "@/hooks/use-blocks"
import { useHeadersFooters } from "@/hooks/use-headers-footers"

interface Props { }

const Page: NextPage<Props> = ({ }) => {
    const router = useRouter()
    const { data: session, status } = useSession()
    const [blocksSearchQuery, setBlocksSearchQuery] = useState("")
    const [debouncedBlocksSearch, setDebouncedBlocksSearch] = useState("")
    // Pagination state for blocks
    const [blocksPage, setBlocksPage] = useState(1)
    const [blocksPageSize, setBlocksPageSize] = useState(10)

    // Debounce blocks search query
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedBlocksSearch(blocksSearchQuery)
        }, 300)

        return () => clearTimeout(timer)
    }, [blocksSearchQuery])

    // Reset blocks page when search changes
    useEffect(() => {
        setBlocksPage(1)
    }, [debouncedBlocksSearch])

    // Fetch blocks with search
    const {
        blocks,
        loading: blocksLoading,
        error: blocksError,
        refetch: refetchBlocks,
        totalCount: blocksTotalCount,
        hasNext: blocksHasNext,
        hasPrevious: blocksHasPrevious
    } = useBlocks({
        search: debouncedBlocksSearch || undefined,
        page: blocksPage,
        pageSize: blocksPageSize
    })

    const handleCreateBlock = () => {
        router.push('/blocks/block/create')
    }

    // Show loading while session is being fetched
    if (status === "loading") {
        return (
            <div className="p-6">
                <div className="flex items-center justify-center h-32">
                    <div className="text-muted-foreground">Loading...</div>
                </div>
            </div>
        )
    }

    // Redirect to login if not authenticated
    if (status === "unauthenticated") {
        router.push('/login')
        return null
    }

    return (
        <div className="p-6 space-y-8">
            {/* Blocks Section */}
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Blocks</h1>
                        <p className="text-muted-foreground">
                            Manage reusable content blocks for your newsletters
                        </p>
                    </div>
                    <Button onClick={handleCreateBlock}>
                        <Plus className="mr-2 h-4 w-4" />
                        Create Block
                    </Button>
                </div>

                <div className="flex items-center space-x-4">
                    <div className="relative flex-1 max-w-sm">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        <Input
                            placeholder="Search blocks..."
                            value={blocksSearchQuery}
                            onChange={(e) => setBlocksSearchQuery(e.target.value)}
                            className="pl-10"
                        />
                    </div>
                </div>

                {blocksError && (
                    <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                        Error: {blocksError}
                    </div>
                )}

                <BlocksTable
                    data={blocks}
                    loading={blocksLoading}
                    onRefresh={refetchBlocks}
                    pagination={{
                        page: blocksPage,
                        pageSize: blocksPageSize,
                        totalCount: blocksTotalCount,
                        hasNext: blocksHasNext,
                        hasPrevious: blocksHasPrevious,
                        onPageChange: setBlocksPage,
                        onPageSizeChange: setBlocksPageSize
                    }}
                />
            </div>
        </div>
    )
}

export default Page