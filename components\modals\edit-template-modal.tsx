"use client"

import React, { useEffect, use<PERSON>em<PERSON>, useState } from "react"
import { <PERSON>ci<PERSON>, GripVertical as GripVerticalIcon } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useBrands } from "@/hooks/use-brands"
import { EditBlockModal } from "@/components/modals/edit-block-modal"
import type { Block } from "@/types/block"
import { useBlocks } from "@/hooks/use-blocks"
import { useTemplateDetail } from "@/hooks/use-template-detail"
import { useUpdateTemplate } from "@/hooks/use-update-template"
import { toast } from "sonner"
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core"
import { SortableContext, arrayMove, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"


interface EditTemplateModalProps {
  templateId: string | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function EditTemplateModal({ templateId, open, onOpenChange, onSuccess }: EditTemplateModalProps) {
  const { brands } = useBrands()
  const [brandFilter, setBrandFilter] = useState<string>("")
  const { blocks, refetch } = useBlocks({ page: 1, pageSize: 100, brand: brandFilter || undefined })
  const { template } = useTemplateDetail(templateId || undefined)
  const { updateTemplate, loading } = useUpdateTemplate()

  // Inline edit block modal state
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [selectedBlock, setSelectedBlock] = useState<Block | null>(null)

  const handleOpenEdit = (blockId: string) => {
    const blk = availableBlocks.find(b => b.id === blockId) || null
    setSelectedBlock(blk)
    setEditModalOpen(!!blk)
  }


  const [formData, setFormData] = useState({
    name: "",
    brand: "",
    description: "",
    is_active: true,
    template_blocks: [] as { block_id: string; order_position: number }[],
  })

  useEffect(() => {
    if (!template) return
    setFormData({
      name: template.name,
      brand: template.brand,
      description: template.description || "",
      is_active: template.is_active,
      template_blocks: template.template_blocks
        .sort((a, b) => a.order_position - b.order_position)
        .map((tb) => ({ block_id: tb.block.id, order_position: tb.order_position })),
    })
    setBrandFilter(template.brand)
  }, [template])

  // Clear selected blocks when brand changes
  useEffect(() => {
    setFormData(prev => ({ ...prev, template_blocks: [] }))
  }, [formData.brand])

  const availableBlocks = useMemo(() => [...blocks].sort((a, b) => a.name.localeCompare(b.name)), [blocks])
  // Build live preview HTML with variables replaced
  const previewHtml = useMemo(() => {
    let html = ""
    for (const tb of formData.template_blocks) {
      const block = availableBlocks.find(b => b.id === tb.block_id)
      if (!block) continue
      let content = block.html_content || ""
      if (block.variables && typeof block.variables === 'object') {
        Object.entries(block.variables).forEach(([varName, defaultValue]) => {
          const regex = new RegExp(`\\{\\{\\s*${varName}\\s*\\}}`, 'g')
          content = content.replace(regex, String(defaultValue ?? ''))
        })
      }
      html += `\n<!-- Bloc: ${block.name} (#${tb.order_position}) -->\n` + content + "\n"
    }
    return html
  }, [formData.template_blocks, availableBlocks])

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(MouseSensor, { activationConstraint: { distance: 5 } }),
    useSensor(TouchSensor),
    useSensor(KeyboardSensor)
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event
    if (!active || !over) return

    setFormData((prev) => {
      const oldIndex = parseInt(String(active.id), 10)
      const newIndex = parseInt(String(over.id), 10)
      if (Number.isNaN(oldIndex) || Number.isNaN(newIndex) || oldIndex === newIndex) return prev
      const moved = arrayMove(prev.template_blocks, oldIndex, newIndex)
      return {
        ...prev,
        template_blocks: moved.map((tb, i) => ({ ...tb, order_position: i + 1 }))
      }
    })
  }

  // Helper component: sortable row for selected blocks table
  function SortableRow({
    id,
    idx,
    name,
    variables,
    onRemove,
    onEdit,
  }: {
    id: string
    idx: number
    name: string
    variables: string[]
    onRemove: () => void
    onEdit: () => void
  }) {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id })

    return (
      <TableRow
        ref={setNodeRef}
        data-dragging={isDragging}
        className="relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80"
        style={{ transform: CSS.Transform.toString(transform), transition }}
      >
        <TableCell className="w-8">
          <Button {...attributes} {...listeners} variant="ghost" size="icon" className="size-7 text-muted-foreground hover:bg-transparent">
            <GripVerticalIcon className="size-3" />
            <span className="sr-only">Arrossegar per reordenar</span>
          </Button>
        </TableCell>
        <TableCell className="w-10 text-muted-foreground text-xs">{idx + 1}</TableCell>
        <TableCell>
          <div className="font-medium">{name}</div>
          {variables.length > 0 && (
            <div className="text-xs text-muted-foreground truncate max-w-[420px]">
              Variables: {variables.sort((a,b)=>a.localeCompare(b)).join(', ')}
            </div>
          )}
        </TableCell>
        <TableCell className="text-right">
          <div className="inline-flex items-center gap-2">
            <Button size="sm" variant="outline" onClick={onEdit}>Editar</Button>
            <Button size="sm" variant="destructive" onClick={onRemove}>Treure</Button>
          </div>
        </TableCell>
      </TableRow>
    )
  }

  const handleAddBlock = (blockId: string) => {
    setFormData((prev) => ({
      ...prev,
      template_blocks: [
        ...prev.template_blocks,
        { block_id: blockId, order_position: prev.template_blocks.length + 1 },
      ],
    }))
  }

  const handleRemoveBlock = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      template_blocks: prev.template_blocks
        .filter((_, i) => i !== index)
        .map((tb, i) => ({ ...tb, order_position: i + 1 })),
    }))
  }



  const handleSubmit = async () => {
    if (!templateId) return
    if (!formData.name.trim()) return toast.error('El nom és obligatori')
    if (!formData.brand) return toast.error('Selecciona una marca')

    try {
      await updateTemplate({ id: templateId, ...formData })
      onSuccess()
      onOpenChange(false)
    } catch (e) {
      console.error(e)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button onClick={handleSubmit} disabled={loading} size="sm">
                {loading ? 'Desant...' : 'Desar canvis'}
              </Button>
              <DialogTitle className="flex items-center gap-2">
                <Pencil className="h-5 w-5" />
                Editar plantilla
              </DialogTitle>
            </div>
            <Button variant="outline" size="sm" onClick={() => onOpenChange(false)}>Cancel·lar</Button>
          </div>
          <DialogDescription>Actualitza la informació i els blocs de la plantilla</DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Informació bàsica</CardTitle>
                <CardDescription>Nom, marca i descripció</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Nom</Label>
                    <Input value={formData.name} onChange={(e) => setFormData((p) => ({ ...p, name: e.target.value }))} />
                  </div>
                  <div className="space-y-2">
                    <Label>Marca</Label>
                    <Select value={formData.brand} onValueChange={(v) => { setBrandFilter(v); setFormData((p) => ({ ...p, brand: v, template_blocks: [] })); }}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecciona una marca" />
                      </SelectTrigger>
                      <SelectContent>
                        {brands.map((b) => (
                          <SelectItem key={b.id} value={b.id}>{b.name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Descripció</Label>
                  <Input value={formData.description} onChange={(e) => setFormData((p) => ({ ...p, description: e.target.value }))} />
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch checked={formData.is_active} onCheckedChange={(v) => setFormData((p) => ({ ...p, is_active: v }))} />
                    <Label className="text-sm">Activa</Label>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Blocs de la plantilla</CardTitle>
                <CardDescription>Afegir, treure i ordenar blocs</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Disable until brand selected */}
                {!formData.brand && (
                  <div className="p-4 text-sm text-muted-foreground border rounded">
                    Selecciona una marca per veure i afegir blocs.
                  </div>
                )}

                {formData.brand && (
                  <div className="overflow-hidden rounded-lg border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50%]">Nom del bloc</TableHead>
                          <TableHead>Marca</TableHead>
                          <TableHead className="text-right">Accions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {availableBlocks.length ? (
                          availableBlocks.map((b) => (
                            <TableRow key={b.id}>
                              <TableCell className="font-medium">{b.name}</TableCell>
                              <TableCell>{b.brand_name}</TableCell>
                              <TableCell className="text-right">
                                <div className="inline-flex items-center gap-2">
                                  <Button size="sm" variant="outline" onClick={() => handleAddBlock(b.id)}>
                                    Afegir
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={3} className="text-center text-sm text-muted-foreground h-16">
                              No hi ha blocs disponibles per a la marca seleccionada.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}

                {/* Selected blocks as sortable table */}
                {formData.template_blocks.length > 0 && (
                  <div className="overflow-hidden rounded-lg border">
                    <DndContext sensors={sensors} onDragEnd={handleDragEnd}>
                      <Table>
                        <TableHeader className="sticky top-0 z-10 bg-muted">
                          <TableRow>
                            <TableHead className="w-8"></TableHead>
                            <TableHead className="w-10">#</TableHead>
                            <TableHead>Bloc</TableHead>
                            <TableHead className="text-right">Accions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {/* Provide ids as indexes for SortableContext */}
                          <SortableContext items={formData.template_blocks.map((tb) => tb.block_id)} strategy={verticalListSortingStrategy}>
                            {formData.template_blocks.map((tb, idx) => {
                              const block = availableBlocks.find((b) => b.id === tb.block_id)
                              const varNames = block && block.variables && typeof block.variables === 'object'
                                ? Object.keys(block.variables)
                                : []
                              return (
                                <SortableRow
                                  key={`${tb.block_id}-${idx}`}
                                  id={tb.block_id}
                                  idx={idx}
                                  name={block?.name || tb.block_id}
                                  variables={varNames}
                                  onRemove={() => handleRemoveBlock(idx)}
                                  onEdit={() => handleOpenEdit(tb.block_id)}
                                />
                              )
                            })}
                          </SortableContext>
                        </TableBody>
                      </Table>
                    </DndContext>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right - sticky preview */}
          <div className="space-y-4 md:col-span-1">
            <div className="sticky top-6 max-h-[calc(100vh-96px)] overflow-y-auto" style={{ zIndex: 10 }}>
              <Card>
                <CardHeader>
                  <CardTitle>Previsualització</CardTitle>
                  <CardDescription>Mostra del resultat concatenant els blocs</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-lg p-4 bg-white min-w-[320px]">
                    {previewHtml ? (
                      <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                    ) : (
                      <div className="text-sm text-muted-foreground">Afegeix blocs per veure la previsualització.</div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

