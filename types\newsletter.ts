export interface Newsletter {
  id: string
  name: string
  brand: string
  brand_name: string
  language: string
  language_display: string
  subject: string
  status: string
  status_display: string
  created_at: string
  updated_at: string
}

export interface NewsletterResponse {
  count: number
  next: string | null
  previous: string | null
  results: Newsletter[]
}

export interface NewsletterTableItem {
  id: string
  header: string
  type: string
  status: string
  target: string
  limit: string
  reviewer: string
}
