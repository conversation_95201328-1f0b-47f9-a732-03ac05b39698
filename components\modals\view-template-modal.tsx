"use client"

import React from "react"
import { Eye } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useTemplateDetail } from "@/hooks/use-template-detail"
import { Spinner } from '@/components/ui/shadcn-io/spinner';

interface ViewTemplateModalProps {
  templateId: string | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ViewTemplateModal({ templateId, open, onOpenChange }: ViewTemplateModalProps) {
  const { template, loading } = useTemplateDetail(templateId || undefined)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Veure plantilla
          </DialogTitle>
          <DialogDescription>Detalls de la plantilla seleccionada</DialogDescription>
        </DialogHeader>

        {loading ? (
        <div className="flex items-center gap-2 mb-4">
          <Spinner key="infinite" variant="infinite" size={32} />
          <span className="font-mono text-muted-foreground text-xs">
            Carregant...
          </span>
        </div>        
        ) : template ? (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-xl font-semibold">{template.name}</div>
                <div className="text-sm text-muted-foreground">Marca: {template.brand_name}</div>
              </div>
              {template.is_active ? (
                <Badge className="bg-green-600 hover:bg-green-600">Activa</Badge>
              ) : (
                <Badge variant="outline" className="border-gray-300 text-gray-600">Inactiva</Badge>
              )}
            </div>

            {template.description && (
              <Card>
                <CardHeader>
                  <CardTitle>Descripció</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm">{template.description}</p>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Blocs ({template.total_blocks})</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <ol className="space-y-2 list-decimal pl-6">
                  {template.template_blocks
                    .sort((a, b) => a.order_position - b.order_position)
                    .map((tb) => (
                      <li key={tb.id} className="flex items-center justify-between">
                        <span>{tb.block.name}</span>
                        <span className="text-xs text-muted-foreground">#{tb.order_position}</span>
                      </li>
                    ))}
                </ol>
                <div className="border rounded-lg p-4 bg-white max-h-[60vh] overflow-auto">
                  {/* Render concatenated HTML preview */}
                  <div dangerouslySetInnerHTML={{ __html: template.template_blocks
                    .sort((a,b)=>a.order_position-b.order_position)
                    .map(tb => tb.block.html_content || "")
                    .join("\n") }} />
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="p-6">No s'han trobat dades de la plantilla.</div>
        )}
      </DialogContent>
    </Dialog>
  )
}

