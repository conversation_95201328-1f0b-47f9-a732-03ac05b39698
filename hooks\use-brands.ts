import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'

export interface Brand {
  id: string
  name: string
}

interface UseBrandsReturn {
  brands: Brand[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useBrands(): UseBrandsReturn {
  const { data: session } = useSession()
  const [brands, setBrands] = useState<Brand[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchBrands = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if user is authenticated
      if (!session) {
        setError('User not authenticated')
        setBrands([])
        return
      }

      if (!session.djangoAccessToken) {
        setError('Django access token not available. Please try logging in again.')
        setBrands([])
        return
      }

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://172.16.249.87/apiv1'

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/brands/list-brands/`, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch brands: ${response.statusText}`)
      }

      const data: Brand[] = await response.json()
      setBrands(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setBrands([])
    } finally {
      setLoading(false)
    }
  }, [session])

  useEffect(() => {
    fetchBrands()
  }, [fetchBrands])

  return {
    brands,
    loading,
    error,
    refetch: fetchBrands
  }
}
